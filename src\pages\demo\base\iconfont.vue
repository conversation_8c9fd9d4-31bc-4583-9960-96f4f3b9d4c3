<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'iconfont',
  },
}
</route>

<template>
  <view class="m-4">
    <text mr-2>iconfont:</text>
    <i class="iconfont icon-package text-primary"></i>
    <i class="iconfont icon-chat text-primary"></i>
    <i class="iconfont icon-my text-primary"></i>
  </view>
</template>

<script lang="ts" setup>
//
</script>

<style lang="scss" scoped>
//
</style>
