{"compilerOptions": {"composite": true, "skipLibCheck": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "outDir": "dist", "lib": ["esnext", "dom"], "types": ["./vite-env.d.ts", "@dcloudio/types", "@types/wechat-miniprogram", "@uni-helper/uni-ui-types", "wot-design-uni/global.d.ts", "@ttou/uv-typings/shim", "@ttou/uv-typings/v2"]}, "vueCompilerOptions": {"target": 3, "nativeTags": ["block", "template", "component", "slot"]}, "exclude": ["node_modules"], "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.json", "mock/_mockProdServer.ts"]}