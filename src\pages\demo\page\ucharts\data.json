{"localdata": [{"value": 35, "text": "2016", "group": "目标值"}, {"value": 18, "text": "2016", "group": "完成量"}, {"value": 36, "text": "2017", "group": "目标值"}, {"value": 27, "text": "2017", "group": "完成量"}, {"value": 31, "text": "2018", "group": "目标值"}, {"value": 21, "text": "2018", "group": "完成量"}, {"value": 33, "text": "2019", "group": "目标值"}, {"value": 24, "text": "2019", "group": "完成量"}, {"value": 13, "text": "2020", "group": "目标值"}, {"value": 6, "text": "2020", "group": "完成量"}, {"value": 34, "text": "2021", "group": "目标值"}, {"value": 28, "text": "2021", "group": "完成量"}], "localdataB": [{"value": 50, "text": "一班"}, {"value": 30, "text": "二班"}, {"value": 20, "text": "三班"}, {"value": 18, "text": "四班"}, {"value": 8, "text": "五班"}], "TLine": {"series": [{"name": "时间轴1", "data": [[10000, 55], [30000, 25], [50000, 55], [70000, 25], [90000, 55]]}, {"name": "时间轴2", "data": [[0, 25], [20000, 55], [40000, 25], [60000, 55], [80000, 25]]}, {"name": "时间轴3", "data": [[0, 55], [15000, 25], [30000, 55], [45000, 25], [60000, 55]]}]}, "Scatter": {"series": [{"name": "散点一", "data": [[10.0, 8.04], [8.07, 6.95], [13.0, 7.58], [9.05, 8.81], [11.0, 8.33], [14.0, 7.66], [13.4, 6.81], [10.0, 6.33], [14.0, 8.96], [12.5, 6.82]]}, {"name": "散点二", "data": [[9.15, 7.2], [11.5, 7.2], [3.03, 4.23], [12.2, 7.83], [2.02, 4.47], [1.05, 3.33], [4.05, 4.96], [6.03, 7.24], [12.0, 6.26], [12.0, 8.84], [7.08, 5.82], [5.02, 5.68]]}]}, "Bubble": {"series": [{"name": "气泡一", "data": [[95, 95, 23, "标题1"], [30, 55, 33, "标题2"]]}, {"name": "气泡二", "data": [[130, 30, 30, "标题3"], [200, 90, 40, "标题4"]]}]}, "Column": {"categories": ["2016", "2017", "2018", "2019", "2020", "2021"], "series": [{"name": "目标值", "data": [35, 36, 31, 33, 13, 34]}, {"name": "完成量", "data": [18, 27, 21, 24, 6, 28]}]}, "ColumnA": {"categories": ["2016", "2017", "2018", "2019", "2020", "2021"], "series": [{"name": "成交量1", "data": [15, {"value": 20, "color": "#f04864"}, 45, 37, 43, 34]}, {"name": "成交量2", "data": [30, {"value": 40, "color": "#facc14"}, 25, 14, 34, 18]}]}, "Mix": {"categories": ["2016", "2017", "2018", "2019", "2020", "2021"], "series": [{"name": "曲面", "data": [70, 50, 85, 130, 64, 88], "type": "area", "style": "curve"}, {"name": "柱1", "index": 1, "data": [40, {"value": 30, "color": "#f04864"}, 55, 110, 24, 58], "type": "column"}, {"name": "柱2", "index": 1, "data": [50, 20, 75, 60, 34, 38], "type": "column"}, {"name": "曲线", "data": [70, 50, 85, 130, 64, 88], "type": "line", "style": "curve", "color": "#1890ff", "disableLegend": true}, {"name": "折线", "data": [120, 140, 105, 170, 95, 160], "type": "line", "color": "#2fc25b"}, {"name": "点", "index": 2, "data": [100, 80, 125, 150, 112, 132], "type": "point", "color": "#f04864"}]}, "Line": {"categories": ["2016", "2017", "2018", "2019", "2020", "2021"], "series": [{"name": "成交量A", "data": [35, 8, 25, 37, 4, 20]}, {"name": "成交量B", "data": [70, 40, 65, 100, 44, 68]}, {"name": "成交量C", "data": [100, 80, 95, 150, 112, 132]}]}, "Pie": {"series": [{"name": "一班", "data": 50}, {"name": "二班", "data": 30}, {"name": "三班", "data": 20}, {"name": "四班", "data": 18}, {"name": "五班", "data": 8}]}, "PieA": {"series": [{"data": [{"name": "一班", "value": 50}, {"name": "二班", "value": 30}, {"name": "三班", "value": 20}, {"name": "四班", "value": 18}, {"name": "五班", "value": 8}]}]}, "Mount": {"series": [{"data": [{"name": "一班", "value": 82}, {"name": "二班", "value": 63}, {"name": "三班", "value": 86}, {"name": "四班", "value": 65}, {"name": "五班", "value": 79}]}]}, "Radar": {"categories": ["维度1", "维度2", "维度3", "维度4", "维度5", "维度6"], "series": [{"name": "成交量1", "data": [90, 110, 165, 195, 187, 172]}, {"name": "成交量2", "data": [190, 210, 105, 35, 27, 102]}]}, "Arcbar1": {"series": [{"name": "正确率", "data": 0.8, "color": "#2fc25b"}]}, "Arcbar2": {"series": [{"name": "一班", "data": 0.8}, {"name": "二班", "data": 0.6}, {"name": "三班", "data": 0.45}, {"name": "四班", "data": 0.3}, {"name": "五班", "data": 0.15}]}, "Gauge": {"categories": [{"value": 0.2, "color": "#1890ff"}, {"value": 0.8, "color": "#2fc25b"}, {"value": 1, "color": "#f04864"}], "series": [{"name": "完成率", "data": 0.66}]}, "Candle": {"categories": ["2020/1/24", "2020/1/25", "2020/1/28", "2020/1/29", "2020/1/30", "2020/1/31", "2020/2/1", "2020/2/4", "2020/2/5", "2020/2/6", "2020/2/7", "2020/2/8", "2020/2/18", "2020/2/19", "2020/2/20", "2020/2/21", "2020/2/22", "2020/2/25", "2020/2/26", "2020/2/27", "2020/2/28", "2020/3/1", "2020/3/4", "2020/3/5", "2020/3/6", "2020/3/7", "2020/3/8", "2020/3/11", "2020/3/12", "2020/3/13", "2020/3/14", "2020/3/15", "2020/3/18", "2020/3/19", "2020/3/20", "2020/3/21", "2020/3/22", "2020/3/25", "2020/3/26", "2020/3/27", "2020/3/28", "2020/3/29", "2020/4/1", "2020/4/2", "2020/4/3", "2020/4/8", "2020/4/9", "2020/4/10", "2020/4/11", "2020/4/12", "2020/4/15", "2020/4/16", "2020/4/17", "2020/4/18", "2020/4/19", "2020/4/22", "2020/4/23", "2020/4/24", "2020/4/25", "2020/4/26", "2020/5/2", "2020/5/3", "2020/5/6", "2020/5/7", "2020/5/8", "2020/5/9", "2020/5/10", "2020/5/13", "2020/5/14", "2020/5/15", "2020/5/16", "2020/5/17", "2020/5/20", "2020/5/21", "2020/5/22", "2020/5/23", "2020/5/24", "2020/5/27", "2020/5/28", "2020/5/29", "2020/5/30", "2020/5/31", "2020/6/3", "2020/6/4", "2020/6/5", "2020/6/6", "2020/6/7", "2020/6/13"], "series": [{"name": "上证指数", "data": [[2320.26, 2302.6, 2287.3, 2362.94], [2300, 2291.3, 2288.26, 2308.38], [2295.35, 2346.5, 2295.35, 2346.92], [2347.22, 2358.98, 2337.35, 2363.8], [2360.75, 2382.48, 2347.89, 2383.76], [2383.43, 2385.42, 2371.23, 2391.82], [2377.41, 2419.02, 2369.57, 2421.15], [2425.92, 2428.15, 2417.58, 2440.38], [2411, 2433.13, 2403.3, 2437.42], [2432.68, 2434.48, 2427.7, 2441.73], [2430.69, 2418.53, 2394.22, 2433.89], [2416.62, 2432.4, 2414.4, 2443.03], [2441.91, 2421.56, 2415.43, 2444.8], [2420.26, 2382.91, 2373.53, 2427.07], [2383.49, 2397.18, 2370.61, 2397.94], [2378.82, 2325.95, 2309.17, 2378.82], [2322.94, 2314.16, 2308.76, 2330.88], [2320.62, 2325.82, 2315.01, 2338.78], [2313.74, 2293.34, 2289.89, 2340.71], [2297.77, 2313.22, 2292.03, 2324.63], [2322.32, 2365.59, 2308.92, 2366.16], [2364.54, 2359.51, 2330.86, 2369.65], [2332.08, 2273.4, 2259.25, 2333.54], [2274.81, 2326.31, 2270.1, 2328.14], [2333.61, 2347.18, 2321.6, 2351.44], [2340.44, 2324.29, 2304.27, 2352.02], [2326.42, 2318.61, 2314.59, 2333.67], [2314.68, 2310.59, 2296.58, 2320.96], [2309.16, 2286.6, 2264.83, 2333.29], [2282.17, 2263.97, 2253.25, 2286.33], [2255.77, 2270.28, 2253.31, 2276.22], [2269.31, 2278.4, 2250, 2312.08], [2267.29, 2240.02, 2239.21, 2276.05], [2244.26, 2257.43, 2232.02, 2261.31], [2257.74, 2317.37, 2257.42, 2317.86], [2318.21, 2324.24, 2311.6, 2330.81], [2321.4, 2328.28, 2314.97, 2332], [2334.74, 2326.72, 2319.91, 2344.89], [2318.58, 2297.67, 2281.12, 2319.99], [2299.38, 2301.26, 2289, 2323.48], [2273.55, 2236.3, 2232.91, 2273.55], [2238.49, 2236.62, 2228.81, 2246.87], [2229.46, 2234.4, 2227.31, 2243.95], [2234.9, 2227.74, 2220.44, 2253.42], [2232.69, 2225.29, 2217.25, 2241.34], [2196.24, 2211.59, 2180.67, 2212.59], [2215.47, 2225.77, 2215.47, 2234.73], [2224.93, 2226.13, 2212.56, 2233.04], [2236.98, 2219.55, 2217.26, 2242.48], [2218.09, 2206.78, 2204.44, 2226.26], [2199.91, 2181.94, 2177.39, 2204.99], [2169.63, 2194.85, 2165.78, 2196.43], [2195.03, 2193.8, 2178.47, 2197.51], [2181.82, 2197.6, 2175.44, 2206.03], [2201.12, 2244.64, 2200.58, 2250.11], [2236.4, 2242.17, 2232.26, 2245.12], [2242.62, 2184.54, 2182.81, 2242.62], [2187.35, 2218.32, 2184.11, 2226.12], [2213.19, 2199.31, 2191.85, 2224.63], [2203.89, 2177.91, 2173.86, 2210.58], [2170.78, 2174.12, 2161.14, 2179.65], [2179.05, 2205.5, 2179.05, 2222.81], [2212.5, 2231.17, 2212.5, 2236.07], [2227.86, 2235.57, 2219.44, 2240.26], [2242.39, 2246.3, 2235.42, 2255.21], [2246.96, 2232.97, 2221.38, 2247.86], [2228.82, 2246.83, 2225.81, 2247.67], [2247.68, 2241.92, 2231.36, 2250.85], [2238.9, 2217.01, 2205.87, 2239.93], [2217.09, 2224.8, 2213.58, 2225.19], [2221.34, 2251.81, 2210.77, 2252.87], [2249.81, 2282.87, 2248.41, 2288.09], [2286.33, 2299.99, 2281.9, 2309.39], [2297.11, 2305.11, 2290.12, 2305.3], [2303.75, 2302.4, 2292.43, 2314.18], [2293.81, 2275.67, 2274.1, 2304.95], [2281.45, 2288.53, 2270.25, 2292.59], [2286.66, 2293.08, 2283.94, 2301.7], [2293.4, 2321.32, 2281.47, 2322.1], [2323.54, 2324.02, 2321.17, 2334.33], [2316.25, 2317.75, 2310.49, 2325.72], [2320.74, 2300.59, 2299.37, 2325.53], [2300.21, 2299.25, 2294.11, 2313.43], [2297.1, 2272.42, 2264.76, 2297.1], [2270.71, 2270.93, 2260.87, 2276.86], [2264.43, 2242.11, 2240.07, 2266.69], [2242.26, 2210.9, 2205.07, 2250.63], [2190.1, 2148.35, 2126.22, 2190.1]]}]}, "CandleColumn": {"categories": ["2020/1/24", "2020/1/25", "2020/1/28", "2020/1/29", "2020/1/30", "2020/1/31", "2020/2/1", "2020/2/4", "2020/2/5", "2020/2/6", "2020/2/7", "2020/2/8", "2020/2/18", "2020/2/19", "2020/2/20", "2020/2/21", "2020/2/22", "2020/2/25", "2020/2/26", "2020/2/27", "2020/2/28", "2020/3/1", "2020/3/4", "2020/3/5", "2020/3/6", "2020/3/7", "2020/3/8", "2020/3/11", "2020/3/12", "2020/3/13", "2020/3/14", "2020/3/15", "2020/3/18", "2020/3/19", "2020/3/20", "2020/3/21", "2020/3/22", "2020/3/25", "2020/3/26", "2020/3/27", "2020/3/28", "2020/3/29", "2020/4/1", "2020/4/2", "2020/4/3", "2020/4/8", "2020/4/9", "2020/4/10", "2020/4/11", "2020/4/12", "2020/4/15", "2020/4/16", "2020/4/17", "2020/4/18", "2020/4/19", "2020/4/22", "2020/4/23", "2020/4/24", "2020/4/25", "2020/4/26", "2020/5/2", "2020/5/3", "2020/5/6", "2020/5/7", "2020/5/8", "2020/5/9", "2020/5/10", "2020/5/13", "2020/5/14", "2020/5/15", "2020/5/16", "2020/5/17", "2020/5/20", "2020/5/21", "2020/5/22", "2020/5/23", "2020/5/24", "2020/5/27", "2020/5/28", "2020/5/29", "2020/5/30", "2020/5/31", "2020/6/3", "2020/6/4", "2020/6/5", "2020/6/6", "2020/6/7", "2020/6/13"], "series": [{"name": "成交量1", "data": [15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45, 37, 43, 15, 20, 45]}]}, "Word": {"series": [{"name": "跨全端图表", "textSize": 25}, {"name": "微信小程序", "textSize": 20}, {"name": "支付宝小程序", "textSize": 20}, {"name": "百度小程序", "textSize": 20}, {"name": "QQ小程序", "textSize": 20}, {"name": "头条小程序", "textSize": 20}, {"name": "抖音小程序", "textSize": 20}, {"name": "360小程序", "textSize": 20}, {"name": "跨全端", "textSize": 10}, {"name": "跨全端", "textSize": 12}, {"name": "跨全端", "textSize": 10}, {"name": "跨全端", "textSize": 12}, {"name": "跨全端", "textSize": 10}, {"name": "跨全端", "textSize": 12}, {"name": "跨全端", "textSize": 10}, {"name": "跨全端", "textSize": 12}]}}